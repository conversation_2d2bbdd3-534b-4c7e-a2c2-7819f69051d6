import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';

// Components
import { Button, Input } from '../../components/common';

// Theme
import { useTheme } from '../../contexts/ThemeContext';

// Store
import { useAuthStore } from '../../store/authStore';

// Types
import { AuthNavigationProp } from '../../types/navigation';
import { UserRole } from '@freela/types';

const RegisterScreen: React.FC = () => {
  const { colors } = useTheme();
  const navigation = useNavigation<AuthNavigationProp>();
  const { register } = useAuthStore();

  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.firstName.trim()) {
      newErrors.firstName = 'الاسم الأول مطلوب';
    }

    if (!formData.lastName.trim()) {
      newErrors.lastName = 'الاسم الأخير مطلوب';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'البريد الإلكتروني مطلوب';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'البريد الإلكتروني غير صحيح';
    }

    if (!formData.password.trim()) {
      newErrors.password = 'كلمة المرور مطلوبة';
    } else if (formData.password.length < 6) {
      newErrors.password = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    }

    if (!formData.confirmPassword.trim()) {
      newErrors.confirmPassword = 'تأكيد كلمة المرور مطلوب';
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'كلمة المرور غير متطابقة';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleRegister = async () => {
    if (!validateForm()) return;

    try {
      setIsLoading(true);
      await register({
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        phone: formData.phone || undefined,
        password: formData.password,
        role: UserRole.CLIENT, // Default to client, can be changed in role selection
      });

      // Navigate to role selection
      navigation.navigate('RoleSelection');
    } catch (error: any) {
      Alert.alert(
        'خطأ في إنشاء الحساب',
        error.message || 'حدث خطأ أثناء إنشاء الحساب. يرجى المحاولة مرة أخرى.',
        [{ text: 'موافق' }]
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.header}>
          <Text style={[styles.title, { color: colors.text }]}>
            إنشاء حساب جديد
          </Text>
          <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
            انضم إلى مجتمع فريلا سوريا
          </Text>
        </View>

        <View style={styles.form}>
          <View style={styles.nameRow}>
            <Input
              label="الاسم الأول"
              placeholder="أدخل اسمك الأول"
              value={formData.firstName}
              onChangeText={(value) => handleInputChange('firstName', value)}
              error={errors.firstName}
              style={styles.nameInput}
            />
            <Input
              label="الاسم الأخير"
              placeholder="أدخل اسمك الأخير"
              value={formData.lastName}
              onChangeText={(value) => handleInputChange('lastName', value)}
              error={errors.lastName}
              style={styles.nameInput}
            />
          </View>

          <Input
            label="البريد الإلكتروني"
            placeholder="أدخل بريدك الإلكتروني"
            value={formData.email}
            onChangeText={(value) => handleInputChange('email', value)}
            error={errors.email}
            keyboardType="email-address"
            autoCapitalize="none"
          />

          <Input
            label="رقم الهاتف (اختياري)"
            placeholder="أدخل رقم هاتفك"
            value={formData.phone}
            onChangeText={(value) => handleInputChange('phone', value)}
            error={errors.phone}
            keyboardType="phone-pad"
          />

          <Input
            label="كلمة المرور"
            placeholder="أدخل كلمة المرور"
            value={formData.password}
            onChangeText={(value) => handleInputChange('password', value)}
            error={errors.password}
            secureTextEntry
          />

          <Input
            label="تأكيد كلمة المرور"
            placeholder="أعد إدخال كلمة المرور"
            value={formData.confirmPassword}
            onChangeText={(value) => handleInputChange('confirmPassword', value)}
            error={errors.confirmPassword}
            secureTextEntry
          />

          <Button
            title="إنشاء الحساب"
            onPress={handleRegister}
            loading={isLoading}
            style={styles.registerButton}
          />

          <View style={styles.loginSection}>
            <Text style={[styles.loginText, { color: colors.textSecondary }]}>
              لديك حساب بالفعل؟{' '}
            </Text>
            <TouchableOpacity onPress={() => navigation.navigate('Login')}>
              <Text style={[styles.loginLink, { color: colors.primary }]}>
                تسجيل الدخول
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 24,
    paddingVertical: 32,
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
  },
  title: {
    fontSize: 28,
    fontFamily: 'Cairo-Bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Cairo-Regular',
    textAlign: 'center',
  },
  form: {
    width: '100%',
  },
  nameRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  nameInput: {
    flex: 1,
    marginHorizontal: 4,
  },
  registerButton: {
    marginTop: 8,
    marginBottom: 24,
  },
  loginSection: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loginText: {
    fontSize: 14,
    fontFamily: 'Cairo-Regular',
  },
  loginLink: {
    fontSize: 14,
    fontFamily: 'Cairo-SemiBold',
  },
});

export default RegisterScreen;
